import { motion, AnimatePresence } from 'framer-motion';
import { FaCalculator, FaCreativeCommonsZero, FaFileInvoiceDollar, FaRobot, FaTachometerAlt, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const sidebarItems = [
  {
    id: 0,
    title: 'Dashboard',
    icon: <FaTachometerAlt size={20} />,
    route: '/dashboard'
  },
  {
    id: 1,
    title: 'Sales Invoice Generator',
    icon: <FaFileInvoiceDollar size={20} />,
    route: '/sales-invoice-generator'
  },
  {
    id: 2,
    title: 'Invoice Automation',
    icon: <FaRobot size={20} />,
    route: '/invoice-automation'
  },
  {
    id: 3,
    title: 'Accounting & Tax Assistant',
    icon: <FaCalculator size={20} />,
    route: '/accounting-tax-assistant'
  },
  {
    id: 4,
    title: 'Xero Automation',
    icon: <FaCreativeCommonsZero size={20} />,
    route: '/xero'
  }
];

export default function Sidebar({ activeItem, setActiveItem, collapsed, onToggle }) {
  return (
    <motion.div
      className="bg-white rounded-xl shadow-lg min-h-screen relative overflow-hidden"
      animate={{
        width: collapsed ? '80px' : '256px'
      }}
      transition={{
        duration: 0.3,
        ease: 'easeInOut'
      }}
    >
      {/* Content Container */}
      <div className="p-4 h-full flex flex-col">
        {/* Logo/Brand */}
        <div className="flex items-center justify-center mb-6 mt-2">
          <AnimatePresence mode="wait">
            {!collapsed ? (
              <motion.h1
                key="full-logo"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
                className="text-2xl font-bold text-transparent bg-clip-text"
                style={{
                  backgroundImage: 'linear-gradient(to right, #735D78, #588B8B)'
                }}
              >
                MizuFlow
              </motion.h1>
            ) : (
              <motion.div
                key="mini-logo"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
                className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-teal-500 flex items-center justify-center"
              >
                <span className="text-white font-bold text-lg">M</span>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Navigation Items */}
        <div className="space-y-2 flex-1">
          {sidebarItems.map((item) => (
            <motion.div
              key={item.id}
              className={`flex items-start gap-3 p-3 rounded-lg cursor-pointer relative ${
                activeItem === item.id
                  ? 'bg-gradient-to-r from-teal-50 to-cyan-50 text-teal-700'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveItem(item.id)}
              whileHover={{
                x: collapsed ? 0 : 5,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.98 }}
              title={collapsed ? item.title : ''}
            >
              <div className={`flex-shrink-0 mt-0.5 ${
                activeItem === item.id
                  ? 'text-teal-700'
                  : 'text-gray-500'
              }`}>
                {item.icon}
              </div>

              <AnimatePresence>
                {!collapsed && (
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                    className="font-medium text-sm leading-tight"
                    style={{
                      maxWidth: '160px',
                      wordWrap: 'break-word',
                      overflowWrap: 'break-word'
                    }}
                  >
                    {item.title}
                  </motion.span>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Toggle Button at Bottom */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={onToggle}
            className="w-full flex items-center justify-center p-2 rounded-lg hover:bg-gray-100 transition-colors text-gray-500 hover:text-gray-700"
            title={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? (
              <FaChevronRight size={16} />
            ) : (
              <div className="flex items-center gap-2">
                <FaChevronLeft size={16} />
                <span className="text-sm font-medium">Collapse</span>
              </div>
            )}
          </button>
        </div>
      </div>
    </motion.div>
  );
}